# Qwen Model Series

Qwen is a large language model series developed by Alibaba Cloud. The series includes both base language models and instruction-tuned language models.

As of late 2023, the series typically included models such as Qwen-7B, Qwen-14B, and Qwen-72B.

The **Qwen3-Embedding** model is specifically designed for text embedding tasks. Its primary function is to convert human-readable text into numerical vectors that effectively capture the semantic meaning and contextual nuances of the text. These embeddings are crucial for various downstream tasks like semantic search, clustering, and recommendation systems.

The **Qwen3-Reranker** helps improve search relevance. After an initial retrieval phase (e.g., from a vector database), the reranker takes the user query and the retrieved documents (or document chunks) and re-scores them based on their true relevance to the query. This process ensures that the most pertinent information is presented to the user or subsequent generation models.

**Ollama** is a versatile and lightweight framework that simplifies the process of running large language models locally. It provides a convenient way to serve models like Qwen locally and integrate them into applications without complex setups.
